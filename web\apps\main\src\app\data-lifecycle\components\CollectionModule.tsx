'use client'

import { useState, useEffect } from 'react'
import {
  Database,
  Server,
  Activity,
  Settings,
  CheckCircle,
  AlertCircle,
  Clock,
  HardDrive,
  Wifi
} from 'lucide-react'

export default function CollectionModule() {
  const [activeTab, setActiveTab] = useState('datasources')
  const [currentTime, setCurrentTime] = useState('')

  // 使用useEffect来设置客户端时间，避免SSR/客户端不匹配
  useEffect(() => {
    setCurrentTime(new Date().toLocaleString())

    // 可选：每分钟更新一次时间
    const interval = setInterval(() => {
      setCurrentTime(new Date().toLocaleString())
    }, 60000)

    return () => clearInterval(interval)
  }, [])

  // Agent运行情况数据
  const agentStats = {
    totalAgents: 24,
    onlineAgents: 22,
    offlineAgents: 2,
    totalDataSources: 156,
    activeDataSources: 142,
    totalDataVolume: '2.8TB',
    lastUpdate: currentTime || '加载中...'
  }

  // 左侧导航菜单项
  const leftMenuItems = [
    {
      id: 'datasources',
      name: '数据源连接',
      icon: Database,
      description: '数据来源单位连接状态'
    },
    {
      id: 'agents',
      name: 'Agent状态',
      icon: Server,
      description: '采集Agent运行监控'
    },
    {
      id: 'monitoring',
      name: '实时监控',
      icon: Activity,
      description: '系统运行状态监控'
    },
    {
      id: 'settings',
      name: '系统配置',
      icon: Settings,
      description: '采集系统参数配置'
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'offline': return <AlertCircle className="w-4 h-4 text-red-500" />
      case 'warning': return <Clock className="w-4 h-4 text-yellow-500" />
      default: return <AlertCircle className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-100 text-green-700 border-green-200'
      case 'offline': return 'bg-red-100 text-red-700 border-red-200'
      case 'warning': return 'bg-yellow-100 text-yellow-700 border-yellow-200'
      default: return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  return (
    <div className="flex min-h-screen">
      {/* 左侧导航菜单 */}
      <div className="w-64 bg-white/80 backdrop-blur-sm border-r border-white/20">
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">数据采集功能</h3>
          <nav className="space-y-2">
            {leftMenuItems.map((item) => {
              const Icon = item.icon
              const isActive = activeTab === item.id
              return (
                <button
                  key={item.id}
                  onClick={() => setActiveTab(item.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 text-left ${
                    isActive
                      ? 'bg-blue-50 text-blue-600 border border-blue-200'
                      : 'text-gray-600 hover:text-blue-600 hover:bg-gray-50'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <div>
                    <div className="font-medium">{item.name}</div>
                    <div className="text-xs text-gray-500">{item.description}</div>
                  </div>
                </button>
              )
            })}
          </nav>
        </div>

        {/* 系统状态概览 */}
        <div className="p-6 border-t border-gray-200">
          <h4 className="text-sm font-semibold text-gray-900 mb-3">系统状态</h4>
          <div className="space-y-3 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Agent在线</span>
              <span className="font-medium text-green-600">{agentStats.onlineAgents}/{agentStats.totalAgents}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">数据源</span>
              <span className="font-medium text-blue-600">{agentStats.activeDataSources}/{agentStats.totalDataSources}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">数据量</span>
              <span className="font-medium text-purple-600">{agentStats.totalDataVolume}</span>
            </div>
          </div>
        </div>
      </div>

      {/* 右侧内容区域 */}
      <div className="flex-1 p-8">
        <div className="max-w-6xl mx-auto">
          {/* 内容标题 */}
          <div className="mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              {leftMenuItems.find(item => item.id === activeTab)?.name}
            </h2>
            <p className="text-gray-600">
              {leftMenuItems.find(item => item.id === activeTab)?.description}
            </p>
          </div>

          {/* 根据选中的标签显示不同内容 */}
          {activeTab === 'datasources' && (
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">数据源连接状态</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* 这里可以添加数据源连接状态的具体内容 */}
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span className="font-medium text-green-700">市政府办公厅</span>
                  </div>
                  <p className="text-sm text-green-600">连接正常 - 3个数据源</p>
                </div>
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span className="font-medium text-green-700">发展改革委</span>
                  </div>
                  <p className="text-sm text-green-600">连接正常 - 4个数据源</p>
                </div>
                <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <Clock className="w-5 h-5 text-yellow-500" />
                    <span className="font-medium text-yellow-700">教育局</span>
                  </div>
                  <p className="text-sm text-yellow-600">连接异常 - 5个数据源</p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'agents' && (
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">Agent运行状态</h3>
              <div className="space-y-4">
                {/* Agent状态列表 */}
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <CheckCircle className="w-5 h-5 text-green-500" />
                      <span className="font-medium">Agent-Gov-001</span>
                    </div>
                    <span className="text-sm text-gray-600">市政府办公厅</span>
                  </div>
                </div>
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <CheckCircle className="w-5 h-5 text-green-500" />
                      <span className="font-medium">Agent-Dev-002</span>
                    </div>
                    <span className="text-sm text-gray-600">发展改革委</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'monitoring' && (
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">实时监控</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600 mb-1">{agentStats.onlineAgents}</div>
                  <div className="text-sm text-blue-700">在线Agent</div>
                </div>
                <div className="p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600 mb-1">{agentStats.activeDataSources}</div>
                  <div className="text-sm text-green-700">活跃数据源</div>
                </div>
                <div className="p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600 mb-1">{agentStats.totalDataVolume}</div>
                  <div className="text-sm text-purple-700">数据总量</div>
                </div>
                <div className="p-4 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600 mb-1">99.2%</div>
                  <div className="text-sm text-orange-700">系统可用性</div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">系统配置</h3>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">采集频率</label>
                  <select className="w-full p-3 border border-gray-300 rounded-lg">
                    <option>每5分钟</option>
                    <option>每10分钟</option>
                    <option>每30分钟</option>
                    <option>每小时</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">数据保留期</label>
                  <select className="w-full p-3 border border-gray-300 rounded-lg">
                    <option>30天</option>
                    <option>90天</option>
                    <option>180天</option>
                    <option>1年</option>
                  </select>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
