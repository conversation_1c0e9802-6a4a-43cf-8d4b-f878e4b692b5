'use client'

import { useState } from 'react'
import { Database, GitMerge, Shield, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

// 导入各模块的组件
import CollectionModule from './components/CollectionModule'
import AggregationModule from './components/AggregationModule'
import GovernanceModule from './components/GovernanceModule'

export default function DataLifecyclePage() {
  const [activeModule, setActiveModule] = useState<'collection' | 'aggregation' | 'governance'>('collection')

  // 顶部菜单配置
  const topMenuItems = [
    {
      id: 'collection',
      name: '数据采集',
      icon: Database,
      description: '多源数据采集接入，支持实时和批量数据导入',
      gradient: 'from-purple-500 to-violet-500'
    },
    {
      id: 'aggregation',
      name: '数据汇聚',
      icon: GitMerge,
      description: '跨系统数据整合汇聚，统一数据标准和格式',
      gradient: 'from-orange-500 to-amber-500'
    },
    {
      id: 'governance',
      name: '数据治理',
      icon: Shield,
      description: '数据质量管控，数据清洗、去重、标准化处理',
      gradient: 'from-red-500 to-rose-500'
    }
  ]

  const renderModuleContent = () => {
    switch (activeModule) {
      case 'collection':
        return <CollectionModule />
      case 'aggregation':
        return <AggregationModule />
      case 'governance':
        return <GovernanceModule />
      default:
        return <CollectionModule />
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 背景装饰 */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-indigo-400/20 to-purple-400/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10">
        {/* 页面头部 */}
        <header className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* 返回按钮 */}
                <Link href="/" className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                  <ArrowLeft className="w-5 h-5" />
                  <span className="font-medium">返回首页</span>
                </Link>
                
                {/* 页面标题 */}
                <div className="flex items-center space-x-3 ml-6">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center shadow-lg">
                    <Database className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                      数据全生命周期管理
                    </h1>
                    <p className="text-sm text-gray-600">从数据采集、清洗、存储到应用的全流程管理</p>
                  </div>
                </div>
              </div>

              {/* 右侧操作 */}
              <div className="flex items-center space-x-4">
                <Link
                  href="/dashboard"
                  className="bg-blue-600 text-white px-4 py-2 rounded-xl hover:bg-blue-700 transition-colors text-sm font-medium"
                >
                  主控台
                </Link>
              </div>
            </div>
          </div>
        </header>

        {/* 顶部导航菜单 */}
        <div className="bg-white/60 backdrop-blur-sm border-b border-white/20">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center space-x-1">
              {topMenuItems.map((item) => {
                const Icon = item.icon
                const isActive = activeModule === item.id
                return (
                  <button
                    key={item.id}
                    onClick={() => setActiveModule(item.id as any)}
                    className={`flex items-center space-x-3 px-6 py-3 rounded-xl transition-all duration-300 ${
                      isActive
                        ? 'bg-white/80 text-blue-600 shadow-lg border border-blue-200/50'
                        : 'text-gray-600 hover:text-blue-600 hover:bg-white/40'
                    }`}
                  >
                    <div className={`w-8 h-8 bg-gradient-to-br ${item.gradient} rounded-lg flex items-center justify-center ${
                      isActive ? 'shadow-lg' : ''
                    }`}>
                      <Icon className="w-4 h-4 text-white" />
                    </div>
                    <div className="text-left">
                      <div className="font-semibold">{item.name}</div>
                      <div className="text-xs text-gray-500 max-w-48 truncate">{item.description}</div>
                    </div>
                  </button>
                )
              })}
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <main className="relative z-10">
          {renderModuleContent()}
        </main>
      </div>
    </div>
  )
}
