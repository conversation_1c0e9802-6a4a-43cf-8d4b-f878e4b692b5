'use client'

import { useState, useEffect } from 'react'
import { TrendingUp, BarChart3, Activity, Target } from 'lucide-react'

interface TrendData {
  monthlyGrowth: Array<{
    month: string
    gdp: number
    population: number
    services: number
  }>
  yearlyComparison: {
    thisYear: number
    lastYear: number
    growth: number
  }
  predictions: Array<{
    indicator: string
    current: number
    predicted: number
    confidence: number
  }>
}

export default function TrendAnalysisModule() {
  const [data, setData] = useState<TrendData>({
    monthlyGrowth: [
      { month: '1月', gdp: 6.2, population: 1.8, services: 12.5 },
      { month: '2月', gdp: 6.8, population: 2.1, services: 15.2 },
      { month: '3月', gdp: 7.2, population: 2.3, services: 18.7 },
      { month: '4月', gdp: 7.8, population: 2.5, services: 22.1 },
      { month: '5月', gdp: 8.1, population: 2.7, services: 25.8 },
      { month: '6月', gdp: 8.5, population: 2.9, services: 28.3 }
    ],
    yearlyComparison: {
      thisYear: 2456.8,
      lastYear: 2267.3,
      growth: 8.4
    },
    predictions: [
      { indicator: 'GDP增长率', current: 8.5, predicted: 9.2, confidence: 85 },
      { indicator: '人口增长', current: 2.9, predicted: 3.1, confidence: 78 },
      { indicator: '服务增长', current: 28.3, predicted: 32.5, confidence: 92 },
      { indicator: '企业注册', current: 15.7, predicted: 18.2, confidence: 88 }
    ]
  })

  useEffect(() => {
    const loadData = () => {
      setData({
        monthlyGrowth: [
          { month: '1月', gdp: 6.2, population: 1.8, services: 12.5 },
          { month: '2月', gdp: 6.8, population: 2.1, services: 15.2 },
          { month: '3月', gdp: 7.2, population: 2.3, services: 18.7 },
          { month: '4月', gdp: 7.8, population: 2.5, services: 22.1 },
          { month: '5月', gdp: 8.1, population: 2.7, services: 25.8 },
          { month: '6月', gdp: 8.5, population: 2.9, services: 28.3 }
        ],
        yearlyComparison: {
          thisYear: 2456.8,
          lastYear: 2267.3,
          growth: 8.4
        },
        predictions: [
          { indicator: 'GDP增长率', current: 8.5, predicted: 9.2, confidence: 85 },
          { indicator: '人口增长', current: 2.9, predicted: 3.1, confidence: 78 },
          { indicator: '服务增长', current: 28.3, predicted: 32.5, confidence: 92 },
          { indicator: '企业注册', current: 15.7, predicted: 18.2, confidence: 88 }
        ]
      })
    }

    const interval = setInterval(loadData, 60000)
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="h-full bg-gradient-to-br from-orange-800/20 to-orange-900/20 backdrop-blur-sm rounded-2xl border border-orange-700/30 p-4">
      <div className="flex items-center space-x-2 mb-4">
        <BarChart3 className="w-5 h-5 text-orange-400" />
        <h3 className="text-lg font-bold text-white">趋势分析</h3>
      </div>

      <div className="space-y-4 h-[calc(100%-60px)] overflow-y-auto">
        {/* 年度对比 */}
        <div className="bg-gray-800/30 rounded-lg p-3">
          <h4 className="text-sm font-semibold text-orange-300 mb-3">年度对比</h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-300">今年GDP</span>
              <span className="text-sm font-bold text-white">{data.yearlyComparison.thisYear}亿</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-300">去年同期</span>
              <span className="text-sm text-gray-400">{data.yearlyComparison.lastYear}亿</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-300">增长率</span>
              <span className="text-sm font-bold text-green-400">+{data.yearlyComparison.growth}%</span>
            </div>
          </div>
        </div>

        {/* 月度趋势图 */}
        <div className="bg-gray-800/30 rounded-lg p-3">
          <h4 className="text-sm font-semibold text-orange-300 mb-3">月度趋势</h4>
          <div className="space-y-3">
            {/* 简化的趋势图 */}
            <div className="h-20 relative">
              <svg className="w-full h-full" viewBox="0 0 100 50">
                {/* GDP趋势线 */}
                <polyline
                  points="10,40 25,35 40,30 55,25 70,20 85,15"
                  fill="none"
                  stroke="rgba(34, 197, 94, 0.8)"
                  strokeWidth="1"
                />
                {/* 人口趋势线 */}
                <polyline
                  points="10,45 25,42 40,39 55,36 70,33 85,30"
                  fill="none"
                  stroke="rgba(59, 130, 246, 0.8)"
                  strokeWidth="1"
                />
                {/* 服务趋势线 */}
                <polyline
                  points="10,35 25,28 40,22 55,18 70,12 85,8"
                  fill="none"
                  stroke="rgba(168, 85, 247, 0.8)"
                  strokeWidth="1"
                />
              </svg>
            </div>
            
            {/* 图例 */}
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-gray-300">GDP</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-gray-300">人口</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span className="text-gray-300">服务</span>
              </div>
            </div>
          </div>
        </div>

        {/* 预测分析 */}
        <div className="bg-gray-800/30 rounded-lg p-3">
          <h4 className="text-sm font-semibold text-orange-300 mb-3">预测分析</h4>
          <div className="space-y-2">
            {data.predictions.map((prediction, index) => (
              <div key={prediction.indicator} className="space-y-1">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-300">{prediction.indicator}</span>
                  <span className="text-orange-300">{prediction.predicted}%</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="flex-1 bg-gray-700 rounded-full h-1.5">
                    <div 
                      className="bg-gradient-to-r from-orange-500 to-orange-400 h-1.5 rounded-full transition-all duration-1000"
                      style={{ width: `${prediction.confidence}%` }}
                    ></div>
                  </div>
                  <span className="text-xs text-gray-400 w-8">{prediction.confidence}%</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 关键指标 */}
        <div className="bg-gray-800/30 rounded-lg p-3">
          <h4 className="text-sm font-semibold text-orange-300 mb-3">关键指标</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="text-center">
              <TrendingUp className="w-4 h-4 text-green-400 mx-auto mb-1" />
              <div className="text-white font-bold">+8.5%</div>
              <div className="text-gray-400">增长率</div>
            </div>
            <div className="text-center">
              <Target className="w-4 h-4 text-blue-400 mx-auto mb-1" />
              <div className="text-white font-bold">92%</div>
              <div className="text-gray-400">目标达成</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
