'use client'

import { useState } from 'react'
import { Settings, BarChart3, GitMerge, Database, CheckCircle, Clock, AlertTriangle } from 'lucide-react'

export default function AggregationModule() {
  const [activeTab, setActiveTab] = useState('tasks')

  // 左侧导航菜单项
  const leftMenuItems = [
    {
      id: 'tasks',
      name: '任务管理',
      icon: Settings,
      description: '数据汇聚任务配置与管理'
    },
    {
      id: 'channels',
      name: '通道管理',
      icon: BarChart3,
      description: '数据传输通道监控'
    },
    {
      id: 'mapping',
      name: '数据映射',
      icon: GitMerge,
      description: '数据字段映射配置'
    },
    {
      id: 'quality',
      name: '质量监控',
      icon: Database,
      description: '数据质量实时监控'
    }
  ]

  // 汇聚任务数据
  const aggregationTasks = [
    {
      id: 1,
      name: '人口基础信息汇聚',
      source: '公安局',
      target: '人口库',
      status: 'running',
      progress: 85,
      lastRun: '2024-01-20 14:30:00',
      records: '1,234,567'
    },
    {
      id: 2,
      name: '企业信息汇聚',
      source: '市场监管局',
      target: '法人库',
      status: 'completed',
      progress: 100,
      lastRun: '2024-01-20 14:25:00',
      records: '456,789'
    },
    {
      id: 3,
      name: '地理信息汇聚',
      source: '自然资源局',
      target: '地理库',
      status: 'error',
      progress: 45,
      lastRun: '2024-01-20 14:20:00',
      records: '789,123'
    }
  ]

  // 通道状态数据
  const channelStats = {
    totalChannels: 12,
    activeChannels: 10,
    errorChannels: 2,
    totalThroughput: '2.3GB/h',
    avgLatency: '150ms'
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <Clock className="w-4 h-4 text-blue-500" />
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'error': return <AlertTriangle className="w-4 h-4 text-red-500" />
      default: return <Clock className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-blue-100 text-blue-700 border-blue-200'
      case 'completed': return 'bg-green-100 text-green-700 border-green-200'
      case 'error': return 'bg-red-100 text-red-700 border-red-200'
      default: return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'running': return '运行中'
      case 'completed': return '已完成'
      case 'error': return '错误'
      default: return '未知'
    }
  }

  return (
    <div className="flex min-h-screen">
      {/* 左侧导航菜单 */}
      <div className="w-64 bg-white/80 backdrop-blur-sm border-r border-white/20">
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">数据汇聚功能</h3>
          <nav className="space-y-2">
            {leftMenuItems.map((item) => {
              const Icon = item.icon
              const isActive = activeTab === item.id
              return (
                <button
                  key={item.id}
                  onClick={() => setActiveTab(item.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 text-left ${
                    isActive
                      ? 'bg-orange-50 text-orange-600 border border-orange-200'
                      : 'text-gray-600 hover:text-orange-600 hover:bg-gray-50'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <div>
                    <div className="font-medium">{item.name}</div>
                    <div className="text-xs text-gray-500">{item.description}</div>
                  </div>
                </button>
              )
            })}
          </nav>
        </div>

        {/* 系统状态概览 */}
        <div className="p-6 border-t border-gray-200">
          <h4 className="text-sm font-semibold text-gray-900 mb-3">汇聚状态</h4>
          <div className="space-y-3 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">活跃通道</span>
              <span className="font-medium text-orange-600">{channelStats.activeChannels}/{channelStats.totalChannels}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">吞吐量</span>
              <span className="font-medium text-blue-600">{channelStats.totalThroughput}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">平均延迟</span>
              <span className="font-medium text-green-600">{channelStats.avgLatency}</span>
            </div>
          </div>
        </div>
      </div>

      {/* 右侧内容区域 */}
      <div className="flex-1 p-8">
        <div className="max-w-6xl mx-auto">
          {/* 内容标题 */}
          <div className="mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              {leftMenuItems.find(item => item.id === activeTab)?.name}
            </h2>
            <p className="text-gray-600">
              {leftMenuItems.find(item => item.id === activeTab)?.description}
            </p>
          </div>

          {/* 根据选中的标签显示不同内容 */}
          {activeTab === 'tasks' && (
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900">汇聚任务列表</h3>
                <button className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors">
                  新建任务
                </button>
              </div>
              <div className="space-y-4">
                {aggregationTasks.map((task) => (
                  <div key={task.id} className="p-6 bg-gray-50 rounded-lg border border-gray-200">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(task.status)}
                        <h4 className="font-semibold text-gray-900">{task.name}</h4>
                        <span className={`px-2 py-1 rounded-full text-xs border ${getStatusColor(task.status)}`}>
                          {getStatusText(task.status)}
                        </span>
                      </div>
                      <div className="text-sm text-gray-600">
                        {task.lastRun}
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <span className="text-sm text-gray-600">数据源：</span>
                        <span className="font-medium">{task.source}</span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-600">目标库：</span>
                        <span className="font-medium">{task.target}</span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-600">记录数：</span>
                        <span className="font-medium">{task.records}</span>
                      </div>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-orange-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${task.progress}%` }}
                      ></div>
                    </div>
                    <div className="text-right text-sm text-gray-600 mt-1">
                      {task.progress}%
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'channels' && (
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">通道监控</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div className="p-4 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600 mb-1">{channelStats.activeChannels}</div>
                  <div className="text-sm text-orange-700">活跃通道</div>
                </div>
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600 mb-1">{channelStats.totalThroughput}</div>
                  <div className="text-sm text-blue-700">总吞吐量</div>
                </div>
                <div className="p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600 mb-1">{channelStats.avgLatency}</div>
                  <div className="text-sm text-green-700">平均延迟</div>
                </div>
                <div className="p-4 bg-red-50 rounded-lg">
                  <div className="text-2xl font-bold text-red-600 mb-1">{channelStats.errorChannels}</div>
                  <div className="text-sm text-red-700">异常通道</div>
                </div>
              </div>
              <div className="space-y-4">
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <CheckCircle className="w-5 h-5 text-green-500" />
                      <span className="font-medium">通道-001</span>
                    </div>
                    <span className="text-sm text-green-600">正常运行</span>
                  </div>
                </div>
                <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <AlertTriangle className="w-5 h-5 text-red-500" />
                      <span className="font-medium">通道-002</span>
                    </div>
                    <span className="text-sm text-red-600">连接异常</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'mapping' && (
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">数据映射配置</h3>
              <div className="space-y-6">
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-3">人口信息映射</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">源字段：</span>
                      <span className="font-medium">person_name</span>
                    </div>
                    <div>
                      <span className="text-gray-600">目标字段：</span>
                      <span className="font-medium">name</span>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-3">企业信息映射</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">源字段：</span>
                      <span className="font-medium">company_name</span>
                    </div>
                    <div>
                      <span className="text-gray-600">目标字段：</span>
                      <span className="font-medium">enterprise_name</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'quality' && (
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">数据质量监控</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600 mb-1">98.5%</div>
                  <div className="text-sm text-green-700">数据完整性</div>
                </div>
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600 mb-1">97.2%</div>
                  <div className="text-sm text-blue-700">数据准确性</div>
                </div>
                <div className="p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600 mb-1">99.1%</div>
                  <div className="text-sm text-purple-700">数据一致性</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
