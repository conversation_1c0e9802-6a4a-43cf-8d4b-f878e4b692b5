/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/monitoring/page";
exports.ids = ["app/monitoring/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmonitoring%2Fpage&page=%2Fmonitoring%2Fpage&appPaths=%2Fmonitoring%2Fpage&pagePath=private-next-app-dir%2Fmonitoring%2Fpage.tsx&appDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmonitoring%2Fpage&page=%2Fmonitoring%2Fpage&appPaths=%2Fmonitoring%2Fpage&pagePath=private-next-app-dir%2Fmonitoring%2Fpage.tsx&appDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?3032\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'monitoring',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/monitoring/page.tsx */ \"(rsc)/./src/app/monitoring/page.tsx\")), \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/monitoring/layout.tsx */ \"(rsc)/./src/app/monitoring/layout.tsx\")), \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/monitoring/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/monitoring/page\",\n        pathname: \"/monitoring\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmonitoring%2Fpage&page=%2Fmonitoring%2Fpage&appPaths=%2Fmonitoring%2Fpage&pagePath=private-next-app-dir%2Fmonitoring%2Fpage.tsx&appDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Cmonitoring%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Cmonitoring%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/monitoring/layout.tsx */ \"(ssr)/./src/app/monitoring/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTQuMi4zMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q1dvcmtzcGFjZSU1QyU1Q3l5LXpzJTVDJTVDd2ViJTVDJTVDYXBwcyU1QyU1Q21haW4lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNtb25pdG9yaW5nJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEtBQTRHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHl1bnl1L21haW4vPzFiZmIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxXb3Jrc3BhY2VcXFxceXktenNcXFxcd2ViXFxcXGFwcHNcXFxcbWFpblxcXFxzcmNcXFxcYXBwXFxcXG1vbml0b3JpbmdcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Cmonitoring%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Cmonitoring%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Cmonitoring%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/monitoring/page.tsx */ \"(ssr)/./src/app/monitoring/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTQuMi4zMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q1dvcmtzcGFjZSU1QyU1Q3l5LXpzJTVDJTVDd2ViJTVDJTVDYXBwcyU1QyU1Q21haW4lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNtb25pdG9yaW5nJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNLQUEwRyIsInNvdXJjZXMiOlsid2VicGFjazovL0B5dW55dS9tYWluLz8zNDFiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcV29ya3NwYWNlXFxcXHl5LXpzXFxcXHdlYlxcXFxhcHBzXFxcXG1haW5cXFxcc3JjXFxcXGFwcFxcXFxtb25pdG9yaW5nXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Cmonitoring%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/monitoring/layout.tsx":
/*!***************************************!*\
  !*** ./src/app/monitoring/layout.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MonitoringSystemLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_FileText_Home_Monitor_Network_Server_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,FileText,Home,Monitor,Network,Server!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_FileText_Home_Monitor_Network_Server_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,FileText,Home,Monitor,Network,Server!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_FileText_Home_Monitor_Network_Server_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,FileText,Home,Monitor,Network,Server!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_FileText_Home_Monitor_Network_Server_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,FileText,Home,Monitor,Network,Server!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_FileText_Home_Monitor_Network_Server_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,FileText,Home,Monitor,Network,Server!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_FileText_Home_Monitor_Network_Server_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,FileText,Home,Monitor,Network,Server!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_FileText_Home_Monitor_Network_Server_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,FileText,Home,Monitor,Network,Server!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction MonitoringSystemLayout({ children }) {\n    const menuItems = [\n        {\n            id: \"home\",\n            label: \"首页\",\n            icon: _barrel_optimize_names_AlertTriangle_BarChart3_FileText_Home_Monitor_Network_Server_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            href: \"/monitoring\"\n        },\n        {\n            id: \"devices\",\n            label: \"设备管理\",\n            icon: _barrel_optimize_names_AlertTriangle_BarChart3_FileText_Home_Monitor_Network_Server_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: \"/monitoring/devices\"\n        },\n        {\n            id: \"realtime\",\n            label: \"实时监控\",\n            icon: _barrel_optimize_names_AlertTriangle_BarChart3_FileText_Home_Monitor_Network_Server_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: \"/monitoring/realtime\"\n        },\n        {\n            id: \"performance\",\n            label: \"性能分析\",\n            icon: _barrel_optimize_names_AlertTriangle_BarChart3_FileText_Home_Monitor_Network_Server_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: \"/monitoring/performance\"\n        },\n        {\n            id: \"network\",\n            label: \"网络监控\",\n            icon: _barrel_optimize_names_AlertTriangle_BarChart3_FileText_Home_Monitor_Network_Server_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: \"/monitoring/network\"\n        },\n        {\n            id: \"logs\",\n            label: \"日志分析\",\n            icon: _barrel_optimize_names_AlertTriangle_BarChart3_FileText_Home_Monitor_Network_Server_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            href: \"/monitoring/logs\"\n        },\n        {\n            id: \"alerts\",\n            label: \"告警管理\",\n            icon: _barrel_optimize_names_AlertTriangle_BarChart3_FileText_Home_Monitor_Network_Server_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            href: \"/monitoring/alerts\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-teal-50 via-cyan-50 to-teal-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_FileText_Home_Monitor_Network_Server_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold bg-gradient-to-r from-teal-600 to-cyan-600 bg-clip-text text-transparent\",\n                                                        children: \"\\uD83D\\uDDA5️ 设备监控系统\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                                                        lineNumber: 70,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"服务器设备实时监控与告警管理平台\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"flex items-center space-x-6\",\n                                        children: menuItems.map((item)=>{\n                                            const Icon = item.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: item.href,\n                                                className: \"flex items-center space-x-2 px-4 py-2 rounded-lg text-gray-700 hover:text-teal-600 hover:bg-teal-50/50 transition-all duration-200 font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                                                        lineNumber: 87,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                                                        lineNumber: 88,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, item.id, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/\",\n                                        className: \"text-gray-600 hover:text-teal-600 transition-colors text-sm font-medium\",\n                                        children: \"返回首页\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/dashboard\",\n                                        className: \"bg-teal-600 text-white px-4 py-2 rounded-xl hover:bg-teal-700 transition-colors text-sm font-medium\",\n                                        children: \"主控台\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative z-10\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 pointer-events-none overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-teal-400/20 to-cyan-400/20 rounded-full blur-3xl animate-float\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-cyan-400/20 to-teal-400/20 rounded-full blur-3xl animate-float\",\n                        style: {\n                            animationDelay: \"2s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\layout.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/monitoring/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/monitoring/page.tsx":
/*!*************************************!*\
  !*** ./src/app/monitoring/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MonitoringPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Calendar,CheckCircle,Clock,Database,Download,Gauge,Network,Plus,RefreshCw,Server,Settings,Shield,Target,TrendingDown,TrendingUp,XCircle,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Calendar,CheckCircle,Clock,Database,Download,Gauge,Network,Plus,RefreshCw,Server,Settings,Shield,Target,TrendingDown,TrendingUp,XCircle,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Calendar,CheckCircle,Clock,Database,Download,Gauge,Network,Plus,RefreshCw,Server,Settings,Shield,Target,TrendingDown,TrendingUp,XCircle,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Calendar,CheckCircle,Clock,Database,Download,Gauge,Network,Plus,RefreshCw,Server,Settings,Shield,Target,TrendingDown,TrendingUp,XCircle,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Calendar,CheckCircle,Clock,Database,Download,Gauge,Network,Plus,RefreshCw,Server,Settings,Shield,Target,TrendingDown,TrendingUp,XCircle,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Calendar,CheckCircle,Clock,Database,Download,Gauge,Network,Plus,RefreshCw,Server,Settings,Shield,Target,TrendingDown,TrendingUp,XCircle,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Calendar,CheckCircle,Clock,Database,Download,Gauge,Network,Plus,RefreshCw,Server,Settings,Shield,Target,TrendingDown,TrendingUp,XCircle,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Calendar,CheckCircle,Clock,Database,Download,Gauge,Network,Plus,RefreshCw,Server,Settings,Shield,Target,TrendingDown,TrendingUp,XCircle,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Calendar,CheckCircle,Clock,Database,Download,Gauge,Network,Plus,RefreshCw,Server,Settings,Shield,Target,TrendingDown,TrendingUp,XCircle,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Calendar,CheckCircle,Clock,Database,Download,Gauge,Network,Plus,RefreshCw,Server,Settings,Shield,Target,TrendingDown,TrendingUp,XCircle,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Calendar,CheckCircle,Clock,Database,Download,Gauge,Network,Plus,RefreshCw,Server,Settings,Shield,Target,TrendingDown,TrendingUp,XCircle,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Calendar,CheckCircle,Clock,Database,Download,Gauge,Network,Plus,RefreshCw,Server,Settings,Shield,Target,TrendingDown,TrendingUp,XCircle,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Calendar,CheckCircle,Clock,Database,Download,Gauge,Network,Plus,RefreshCw,Server,Settings,Shield,Target,TrendingDown,TrendingUp,XCircle,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Calendar,CheckCircle,Clock,Database,Download,Gauge,Network,Plus,RefreshCw,Server,Settings,Shield,Target,TrendingDown,TrendingUp,XCircle,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/gauge.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Calendar,CheckCircle,Clock,Database,Download,Gauge,Network,Plus,RefreshCw,Server,Settings,Shield,Target,TrendingDown,TrendingUp,XCircle,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Calendar,CheckCircle,Clock,Database,Download,Gauge,Network,Plus,RefreshCw,Server,Settings,Shield,Target,TrendingDown,TrendingUp,XCircle,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Calendar,CheckCircle,Clock,Database,Download,Gauge,Network,Plus,RefreshCw,Server,Settings,Shield,Target,TrendingDown,TrendingUp,XCircle,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Calendar,CheckCircle,Clock,Database,Download,Gauge,Network,Plus,RefreshCw,Server,Settings,Shield,Target,TrendingDown,TrendingUp,XCircle,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Calendar,CheckCircle,Clock,Database,Download,Gauge,Network,Plus,RefreshCw,Server,Settings,Shield,Target,TrendingDown,TrendingUp,XCircle,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Calendar,CheckCircle,Clock,Database,Download,Gauge,Network,Plus,RefreshCw,Server,Settings,Shield,Target,TrendingDown,TrendingUp,XCircle,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction MonitoringPage() {\n    // 设备台账统计\n    const deviceStats = [\n        {\n            label: \"设备总数\",\n            value: \"156\",\n            change: \"+12\",\n            changeType: \"increase\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            color: \"blue\",\n            description: \"本月新增12台设备\"\n        },\n        {\n            label: \"在线设备\",\n            value: \"142\",\n            total: \"156\",\n            percentage: \"91%\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: \"green\",\n            description: \"14台设备离线\"\n        },\n        {\n            label: \"告警设备\",\n            value: \"8\",\n            change: \"-3\",\n            changeType: \"decrease\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: \"orange\",\n            description: \"较昨日减少3台\"\n        },\n        {\n            label: \"异常设备\",\n            value: \"2\",\n            change: \"+1\",\n            changeType: \"increase\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"red\",\n            description: \"需要立即处理\"\n        }\n    ];\n    // 性能指标概览\n    const performanceMetrics = [\n        {\n            label: \"CPU平均使用率\",\n            value: \"68\",\n            unit: \"%\",\n            trend: \"up\",\n            status: \"warning\",\n            target: \"< 80%\"\n        },\n        {\n            label: \"内存平均使用率\",\n            value: \"72\",\n            unit: \"%\",\n            trend: \"up\",\n            status: \"warning\",\n            target: \"< 85%\"\n        },\n        {\n            label: \"磁盘平均使用率\",\n            value: \"45\",\n            unit: \"%\",\n            trend: \"stable\",\n            status: \"normal\",\n            target: \"< 90%\"\n        },\n        {\n            label: \"网络平均延迟\",\n            value: \"12\",\n            unit: \"ms\",\n            trend: \"down\",\n            status: \"good\",\n            target: \"< 50ms\"\n        }\n    ];\n    // 告警统计\n    const alertStats = [\n        {\n            type: \"严重\",\n            count: 2,\n            color: \"red\",\n            percentage: 15\n        },\n        {\n            type: \"警告\",\n            count: 8,\n            color: \"orange\",\n            percentage: 62\n        },\n        {\n            type: \"信息\",\n            count: 3,\n            color: \"blue\",\n            percentage: 23\n        }\n    ];\n    // 设备类型分布\n    const deviceTypes = [\n        {\n            type: \"Web服务器\",\n            count: 45,\n            percentage: 29,\n            color: \"blue\"\n        },\n        {\n            type: \"数据库服务器\",\n            count: 32,\n            percentage: 21,\n            color: \"green\"\n        },\n        {\n            type: \"应用服务器\",\n            count: 38,\n            percentage: 24,\n            color: \"purple\"\n        },\n        {\n            type: \"文件服务器\",\n            count: 25,\n            percentage: 16,\n            color: \"orange\"\n        },\n        {\n            type: \"其他设备\",\n            count: 16,\n            percentage: 10,\n            color: \"gray\"\n        }\n    ];\n    // 监控子功能模块\n    const monitoringModules = [\n        {\n            name: \"实时监控\",\n            description: \"设备实时状态监控，CPU、内存、磁盘等指标实时展示\",\n            href: \"/monitoring/realtime\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            gradient: \"from-blue-500 to-cyan-500\",\n            features: [\n                \"实时数据\",\n                \"性能图表\",\n                \"状态告警\"\n            ]\n        },\n        {\n            name: \"性能分析\",\n            description: \"设备性能趋势分析，历史数据对比和性能优化建议\",\n            href: \"/monitoring/performance\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            gradient: \"from-green-500 to-emerald-500\",\n            features: [\n                \"趋势分析\",\n                \"性能报告\",\n                \"优化建议\"\n            ]\n        },\n        {\n            name: \"告警管理\",\n            description: \"告警规则配置，告警历史查询和告警处理流程管理\",\n            href: \"/monitoring/alerts\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            gradient: \"from-orange-500 to-red-500\",\n            features: [\n                \"告警规则\",\n                \"历史记录\",\n                \"处理流程\"\n            ]\n        },\n        {\n            name: \"设备管理\",\n            description: \"设备台账管理，设备信息维护和设备生命周期管理\",\n            href: \"/monitoring/devices\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            gradient: \"from-purple-500 to-violet-500\",\n            features: [\n                \"设备台账\",\n                \"信息维护\",\n                \"生命周期\"\n            ]\n        },\n        {\n            name: \"网络监控\",\n            description: \"网络拓扑监控，网络流量分析和网络设备状态监控\",\n            href: \"/monitoring/network\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            gradient: \"from-indigo-500 to-blue-500\",\n            features: [\n                \"网络拓扑\",\n                \"流量分析\",\n                \"连接状态\"\n            ]\n        },\n        {\n            name: \"日志分析\",\n            description: \"系统日志收集分析，异常日志检测和日志统计报表\",\n            href: \"/monitoring/logs\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            gradient: \"from-teal-500 to-cyan-500\",\n            features: [\n                \"日志收集\",\n                \"异常检测\",\n                \"统计报表\"\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full px-4 py-8 space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                children: \"设备监控总览\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-lg text-gray-600\",\n                                children: \"全面监控各类设备运行状态，实时掌握系统性能和健康状况\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"btn-secondary flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"刷新数据\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"btn-primary flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"添加设备\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"设备台账概览\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"最后更新: 2024-01-15 14:30\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: deviceStats.map((stat, index)=>{\n                            const Icon = stat.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card group hover:shadow-xl transition-all duration-500\",\n                                style: {\n                                    animationDelay: `${index * 100}ms`\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-content\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-12 h-12 rounded-2xl flex items-center justify-center ${stat.color === \"blue\" ? \"bg-blue-100\" : stat.color === \"green\" ? \"bg-green-100\" : stat.color === \"orange\" ? \"bg-orange-100\" : \"bg-red-100\"}`,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                className: `w-6 h-6 ${stat.color === \"blue\" ? \"text-blue-600\" : stat.color === \"green\" ? \"text-green-600\" : stat.color === \"orange\" ? \"text-orange-600\" : \"text-red-600\"}`\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: stat.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                    lineNumber: 242,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-baseline space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-3xl font-bold text-gray-900\",\n                                                                            children: stat.value\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                            lineNumber: 244,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        stat.total && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg text-gray-500\",\n                                                                            children: [\n                                                                                \"/\",\n                                                                                stat.total\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                            lineNumber: 246,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        stat.percentage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: `text-sm font-medium ${stat.color === \"green\" ? \"text-green-600\" : \"text-gray-500\"}`,\n                                                                            children: [\n                                                                                \"(\",\n                                                                                stat.percentage,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                            lineNumber: 249,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                    lineNumber: 243,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 23\n                                                }, this),\n                                                stat.change && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        stat.changeType === \"increase\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: `w-4 h-4 ${stat.color === \"red\" ? \"text-red-500\" : \"text-green-500\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 29\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-4 h-4 text-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `text-sm font-medium ${stat.changeType === \"increase\" && stat.color === \"red\" ? \"text-red-600\" : \"text-green-600\"}`,\n                                                            children: stat.change\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-2\",\n                                                    children: stat.description\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 17\n                                }, this)\n                            }, stat.label, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"实时监控情况\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 bg-green-500 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"实时更新中\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-blue-600 hover:text-blue-700 text-sm font-medium\",\n                                        children: \"查看详细监控 →\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: performanceMetrics.map((metric, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card group hover:shadow-xl transition-all duration-500\",\n                                style: {\n                                    animationDelay: `${index * 100}ms`\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: metric.label\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        metric.trend === \"up\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-4 h-4 text-red-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 47\n                                                        }, this),\n                                                        metric.trend === \"down\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-4 h-4 text-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        metric.trend === \"stable\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-1 bg-gray-400 rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 51\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-baseline space-x-2 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold text-gray-900\",\n                                                    children: metric.value\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg text-gray-500\",\n                                                    children: metric.unit\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500\",\n                                                    children: [\n                                                        \"目标: \",\n                                                        metric.target\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `px-2 py-1 rounded-full font-medium ${metric.status === \"good\" ? \"bg-green-100 text-green-700\" : metric.status === \"normal\" ? \"bg-blue-100 text-blue-700\" : \"bg-orange-100 text-orange-700\"}`,\n                                                    children: metric.status === \"good\" ? \"良好\" : metric.status === \"normal\" ? \"正常\" : \"警告\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `h-2 rounded-full transition-all duration-1000 ${metric.status === \"good\" ? \"bg-green-500\" : metric.status === \"normal\" ? \"bg-blue-500\" : \"bg-orange-500\"}`,\n                                                    style: {\n                                                        width: `${metric.value}%`\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 15\n                                }, this)\n                            }, metric.label, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-header\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: \"告警概览\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"今日\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-red-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-red-600\",\n                                                            children: \"13条告警\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: alertStats.map((alert, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `w-4 h-4 rounded-full ${alert.color === \"red\" ? \"bg-red-500\" : alert.color === \"orange\" ? \"bg-orange-500\" : \"bg-blue-500\"}`\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: [\n                                                                    alert.type,\n                                                                    \"告警\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg font-bold text-gray-900\",\n                                                                children: alert.count\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 bg-gray-200 rounded-full h-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `h-2 rounded-full ${alert.color === \"red\" ? \"bg-red-500\" : alert.color === \"orange\" ? \"bg-orange-500\" : \"bg-blue-500\"}`,\n                                                                    style: {\n                                                                        width: `${alert.percentage}%`\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-500 w-8\",\n                                                                children: [\n                                                                    alert.percentage,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, alert.type, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 pt-4 border-t border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"处理率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-green-600\",\n                                                        children: \"85%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 w-full bg-gray-200 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-500 h-2 rounded-full\",\n                                                    style: {\n                                                        width: \"85%\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-header\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: \"设备类型分布\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"text-blue-600 hover:text-blue-700 text-sm font-medium\",\n                                            children: \"查看详情 →\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: deviceTypes.map((device, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `w-4 h-4 rounded-full ${device.color === \"blue\" ? \"bg-blue-500\" : device.color === \"green\" ? \"bg-green-500\" : device.color === \"purple\" ? \"bg-purple-500\" : device.color === \"orange\" ? \"bg-orange-500\" : \"bg-gray-500\"}`\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: device.type\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg font-bold text-gray-900\",\n                                                                children: device.count\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 bg-gray-200 rounded-full h-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `h-2 rounded-full ${device.color === \"blue\" ? \"bg-blue-500\" : device.color === \"green\" ? \"bg-green-500\" : device.color === \"purple\" ? \"bg-purple-500\" : device.color === \"orange\" ? \"bg-orange-500\" : \"bg-gray-500\"}`,\n                                                                    style: {\n                                                                        width: `${device.percentage}%`\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-500 w-8\",\n                                                                children: [\n                                                                    device.percentage,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, device.type, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 pt-4 border-t border-gray-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: \"156\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"设备总数\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                        lineNumber: 410,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"时间维度概览\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 bg-gray-100 rounded-lg p-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-3 py-1 text-sm font-medium bg-white text-gray-900 rounded shadow-sm\",\n                                                children: \"今天\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-3 py-1 text-sm font-medium text-gray-600 hover:text-gray-900\",\n                                                children: \"近7天\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-3 py-1 text-sm font-medium text-gray-600 hover:text-gray-900\",\n                                                children: \"近30天\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn-secondary flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"自定义时间\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card-header\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"今日概览\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card-content\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"新增告警\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-bold text-red-600\",\n                                                            children: \"13\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"处理告警\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-bold text-green-600\",\n                                                            children: \"11\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"设备重启\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-bold text-orange-600\",\n                                                            children: \"3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 499,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"平均响应时间\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-bold text-blue-600\",\n                                                            children: \"12ms\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card-header\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"近期趋势\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card-content\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"设备可用性\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                    lineNumber: 522,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg font-bold text-green-600\",\n                                                                    children: \"+2.1%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"平均负载\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                    lineNumber: 529,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg font-bold text-green-600\",\n                                                                    children: \"-5.3%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                    lineNumber: 530,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"告警频率\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                    lineNumber: 536,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg font-bold text-green-600\",\n                                                                    children: \"-12%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                    lineNumber: 537,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"处理效率\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                    lineNumber: 543,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg font-bold text-green-600\",\n                                                                    children: \"+8.7%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card-header\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-5 h-5 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"性能指标\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                            lineNumber: 554,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card-content\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold text-gray-900 mb-1\",\n                                                            children: \"99.2%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 562,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"系统可用性\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 w-full bg-gray-200 rounded-full h-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-green-500 h-2 rounded-full\",\n                                                                style: {\n                                                                    width: \"99.2%\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 pt-4 border-t border-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xl font-bold text-blue-600\",\n                                                                    children: \"142\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                    lineNumber: 570,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: \"在线设备\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xl font-bold text-orange-600\",\n                                                                    children: \"8\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                    lineNumber: 574,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: \"告警设备\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                    lineNumber: 575,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                        lineNumber: 478,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                lineNumber: 462,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-indigo-50 px-8 py-4 rounded-2xl mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"w-8 h-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 588,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                        children: \"设备监控功能模块\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 587,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"选择您需要的监控功能\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                children: \"点击下方功能模块，进入对应的设备监控子功能页面，获得更详细的监控数据和管理功能\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                        lineNumber: 586,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-blue-500/10 to-indigo-500/10 rounded-3xl p-8 border-2 border-blue-200/50 shadow-2xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: monitoringModules.map((module, index)=>{\n                                    const Icon = module.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: module.href,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/90 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 cursor-pointer group transform hover:scale-105 border border-white/50\",\n                                            style: {\n                                                animationDelay: `${index * 100}ms`\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative overflow-hidden text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `absolute inset-0 bg-gradient-to-br ${module.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500 rounded-3xl`\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                        lineNumber: 612,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative z-10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `w-20 h-20 bg-gradient-to-br ${module.gradient} rounded-3xl mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110`,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"w-10 h-10 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                    lineNumber: 616,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-700 transition-colors\",\n                                                                children: module.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                lineNumber: 619,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-sm mb-6 leading-relaxed\",\n                                                                children: module.description\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                lineNumber: 623,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2 mb-6\",\n                                                                children: module.features.map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-center text-xs text-gray-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-green-500 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                                lineNumber: 630,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            feature\n                                                                        ]\n                                                                    }, idx, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                        lineNumber: 629,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                lineNumber: 627,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg font-bold text-blue-600 group-hover:text-blue-700 transition-colors flex items-center\",\n                                                                    children: [\n                                                                        \"进入模块\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                            lineNumber: 639,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                    lineNumber: 637,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                                lineNumber: 636,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white/50 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-1000\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, module.name, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mt-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center space-x-3 text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                            lineNumber: 658,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg\",\n                                            children: \"全方位设备监控，确保系统稳定运行\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                    lineNumber: 657,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 656,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                        lineNumber: 600,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                lineNumber: 585,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-gradient-to-r from-gray-50 to-blue-50 rounded-3xl p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                children: \"快速操作\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 668,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"常用的监控管理操作，一键直达\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 669,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                        lineNumber: 667,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: [\n                            {\n                                name: \"导出报表\",\n                                icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                                color: \"blue\",\n                                description: \"导出监控数据报表\"\n                            },\n                            {\n                                name: \"配置告警\",\n                                icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                color: \"orange\",\n                                description: \"设置告警规则和阈值\"\n                            },\n                            {\n                                name: \"系统诊断\",\n                                icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                color: \"green\",\n                                description: \"运行系统健康检查\"\n                            },\n                            {\n                                name: \"性能优化\",\n                                icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Calendar_CheckCircle_Clock_Database_Download_Gauge_Network_Plus_RefreshCw_Server_Settings_Shield_Target_TrendingDown_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                                color: \"purple\",\n                                description: \"获取性能优化建议\"\n                            }\n                        ].map((action, index)=>{\n                            const Icon = action.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group text-center border border-white/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-12 h-12 bg-${action.color}-100 rounded-2xl mx-auto mb-4 flex items-center justify-center group-hover:scale-110 transition-transform`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: `w-6 h-6 text-${action.color}-600`\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-2\",\n                                        children: action.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 688,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: action.description\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, action.name, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                        lineNumber: 672,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n                lineNumber: 666,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\monitoring\\\\page.tsx\",\n        lineNumber: 184,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/monitoring/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0d5fe442f342\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHl1bnl1L21haW4vLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2U0MmYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwZDVmZTQ0MmYzNDJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"云宇政数平台\",\n    description: \"统一的政数局数据管理平台 - 大屏展示、报表分析、数据采集、数据汇聚、数据清洗治理、数据资源池管理、设备监控\",\n    keywords: \"政数局,数据平台,大屏,报表,数据采集,数据治理,设备监控\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans\",\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxVQUFVO0FBQ1osRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFVO3NCQUNiSjs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsid2VicGFjazovL0B5dW55dS9tYWluLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAn5LqR5a6H5pS/5pWw5bmz5Y+wJyxcbiAgZGVzY3JpcHRpb246ICfnu5/kuIDnmoTmlL/mlbDlsYDmlbDmja7nrqHnkIblubPlj7AgLSDlpKflsY/lsZXnpLrjgIHmiqXooajliIbmnpDjgIHmlbDmja7ph4fpm4bjgIHmlbDmja7msYfogZrjgIHmlbDmja7muIXmtJfmsrvnkIbjgIHmlbDmja7otYTmupDmsaDnrqHnkIbjgIHorr7lpIfnm5HmjqcnLFxuICBrZXl3b3JkczogJ+aUv+aVsOWxgCzmlbDmja7lubPlj7As5aSn5bGPLOaKpeihqCzmlbDmja7ph4fpm4Ys5pWw5o2u5rK755CGLOiuvuWkh+ebkeaOpycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiZm9udC1zYW5zXCI+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/monitoring/layout.tsx":
/*!***************************************!*\
  !*** ./src/app/monitoring/layout.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Workspace\yy-zs\web\apps\main\src\app\monitoring\layout.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/monitoring/page.tsx":
/*!*************************************!*\
  !*** ./src/app/monitoring/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Workspace\yy-zs\web\apps\main\src\app\monitoring\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/lucide-react@0.294.0_react@18.3.1","vendor-chunks/@swc+helpers@0.5.5"], () => (__webpack_exec__("(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmonitoring%2Fpage&page=%2Fmonitoring%2Fpage&appPaths=%2Fmonitoring%2Fpage&pagePath=private-next-app-dir%2Fmonitoring%2Fpage.tsx&appDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();